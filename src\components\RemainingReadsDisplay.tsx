import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';
import { getFontClass } from '../utils/fontUtils';

interface User {
  vipStatus: string;
  remainingReads: number;
}

interface RemainingReadsDisplayProps {
  user: User | null;
  onLoginClick?: () => void;
  className?: string;
}

const RemainingReadsDisplay: React.FC<RemainingReadsDisplayProps> = ({
  user,
  onLoginClick,
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [anonymousReadsUsed, setAnonymousReadsUsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const isDark = theme === 'dark';

  // 检查匿名用户状态
  useEffect(() => {
    const checkAnonymousStatus = async () => {
      if (!user) {
        try {
          const fingerprint = await getBrowserFingerprint();
          const eligibility = await checkAnonymousEligibility(fingerprint);
          setAnonymousReadsUsed(eligibility.hasUsed);
        } catch (error) {
          console.error('检查匿名用户状态失败:', error);
          setAnonymousReadsUsed(false);
        }
      }
      setLoading(false);
    };

    checkAnonymousStatus();
  }, [user]);

  const handleLoginClick = () => {
    if (onLoginClick) {
      onLoginClick();
    } else {
      navigate('/login');
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className={`h-6 rounded ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
      </div>
    );
  }

  if (!user) {
    // 未登录状态
    if (anonymousReadsUsed) {
      return (
        <div className={`flex items-center justify-center ${className}`}>
          <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
            ${isDark
              ? 'bg-red-900/30 text-red-200 border border-red-500/30 hover:bg-red-900/40'
              : 'bg-red-50 text-red-700 border border-red-200 hover:bg-red-100'
            }`}>
            <svg
              className={`w-4 h-4 ${isDark ? 'text-red-300' : 'text-red-600'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <span>
              {t('anonymous.reads_exhausted', '您的免费占卜次数已用完')}
            </span>
            <button
              onClick={handleLoginClick}
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                isDark
                  ? 'bg-purple-600 hover:bg-purple-700 text-white border border-purple-500/30'
                  : 'bg-purple-500 hover:bg-purple-600 text-white border border-purple-200'
              }`}
            >
              {t('anonymous.login_for_more', '登录获取更多')}
            </button>
          </div>
        </div>
      );
    } else {
      return (
        <div className={`flex items-center justify-center ${className}`}>
          <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
            ${isDark
              ? 'bg-green-900/30 text-green-200 border border-green-500/30 hover:bg-green-900/40'
              : 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100'
            }`}>
            <svg
              className={`w-4 h-4 ${isDark ? 'text-green-300' : 'text-green-600'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            <span>
              {t('anonymous.free_reads_available', '剩余免费占卜次数：1次')}
            </span>
          </div>
        </div>
      );
    }
  }
  
  // 已登录状态
  if (user.vipStatus === 'active') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`px-3 py-1 rounded-full text-sm ${
          isDark
            ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-700'
            : 'bg-yellow-100 text-yellow-700 border border-yellow-200'
        }`}>
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            {t('user.vip_unlimited', 'VIP用户 - 无限次占卜')}
          </span>
        </div>
      </div>
    );
  }

  const remainingReads = Math.max(0, user.remainingReads);
  const isLowReads = remainingReads <= 1;

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`px-3 py-1 rounded-full text-sm ${
        isLowReads
          ? isDark
            ? 'bg-red-900/30 text-red-400 border border-red-700'
            : 'bg-red-100 text-red-700 border border-red-200'
          : isDark
            ? 'bg-blue-900/30 text-blue-400 border border-blue-700'
            : 'bg-blue-100 text-blue-700 border border-blue-200'
      }`}>
        {t('user.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingReads })}
      </div>
      {isLowReads && (
        <button
          onClick={() => navigate('/membership')}
          className={`ml-2 px-2 py-1 rounded text-xs transition-colors ${
            isDark
              ? 'bg-purple-600 hover:bg-purple-700 text-white'
              : 'bg-purple-500 hover:bg-purple-600 text-white'
          }`}
        >
          {t('user.upgrade_vip', '升级VIP')}
        </button>
      )}
    </div>
  );
};

export default RemainingReadsDisplay;
